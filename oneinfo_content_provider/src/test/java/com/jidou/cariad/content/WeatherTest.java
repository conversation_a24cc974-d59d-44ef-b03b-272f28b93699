package com.jidou.cariad.content;

import com.jidou.cariad.content.base.weather.dao.entity.WeatherAlarmDO;
import com.jidou.cariad.content.common.cp.NmcUtil;
import com.jidou.cariad.content.common.enums.ProjectVersion;
import com.jidou.cariad.content.model.vo.weather.WeatherInfo;
import com.jidou.cariad.content.service.WeatherService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/16 19:24
 */

@RunWith(SpringRunner.class)
@SpringBootTest(classes = OneinfoContentProviderApplication.class)
@Slf4j
public class WeatherTest {
    @Resource
    WeatherService weatherService;
    @Resource
    NmcUtil nmcUtil;

    @Test
    public void weatherTest() {
        WeatherInfo result = weatherService.getWeather("潜江市", ProjectVersion.V1);
        log.info("result:{}", result.toString());
    }


    @Test
    public void getAlarm() {
        List<WeatherAlarmDO> alarmDOList = weatherService.searchAlarm("广州增城");
    }

    @Test
    public void syncNmc() {
        weatherService.syncNmc();
    }

    @Test
    public void getnmcUrl() {
        String s = nmcUtil.buildWeatherUrl("浑源,大同,山西,中国",true);
    }
}
