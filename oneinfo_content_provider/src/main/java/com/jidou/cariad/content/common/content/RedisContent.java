package com.jidou.cariad.content.common.content;

import lombok.experimental.UtilityClass;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.TimeZone;

/**
 * <AUTHOR>
 * @since 2023/11/06 17:54
 */
@UtilityClass
public class RedisContent {


    public static final String EXCHANGE_SINGLE = "content:exchange:single";
    public static final String EXCHANGE_CONVERT = "content:exchange:convert";
    // 大模型相关
    public static final String LLM_TOKEN = "content:llm:token";
    // 百科相关
    public static final String BAIKE_SOLAR_TERM = "content:baike:solar_term";
    public static final String BAIKE_CITY = "content:baike:city";
    // 天气相关
    public static final String WEATHER_LOCATIONID = "content:weather:locationId";
    public static final String WEATHER_NOW = "content:weather:now";
    public static final String WEATHER_DAILY = "content:weather:daily";
    public static final String WEATHER_HOURLY = "content:weather:hourly";
    public static final String WEATHER_ALARM = "content:weather:alarm";
    public static final String WEATHER_HISTORY = "content:weather:history";
    public static final String WEATHER_ALARM_TYPE_MAPPING = "content:weather:alarm:type:mapping";
    // 风速数据相关
    public static final String WEATHER_WIND_LOCK_PREFIX = "content:weather:wind:lock:";
    public static final String WEATHER_WIND_HOURLY_PREFIX = "content:weather:wind:hourly:";
    public static final String WEATHER_WIND_REQUEST_QUEUE = "content:weather:wind:request:queue";
    // 风速数据限流分离 - 调度场景和响应场景独立限流
    public static final String WEATHER_WIND_SCHEDULE_LIMIT_PREFIX = "content:weather:wind:schedule:limit:";
    public static final String WEATHER_WIND_RESPONSE_LIMIT_PREFIX = "content:weather:wind:response:limit:";
    // 城市调度缓存相关
    public static final String WEATHER_CITY_SCHEDULE_CACHE_PREFIX = "content:weather:wind:city:schedule:";
    // 风速数据URL映射缓存相关
    public static final String WEATHER_WIND_URL_MAPPING_PREFIX = "content:weather:wind:url:mapping:";
    // 风速数据URL级别缓存相关
    public static final String WEATHER_WIND_URL_DATA_PREFIX = "content:weather:wind:url:data:";
    // 空气质量相关
    public static final String AIR_NOW = "content:air:now";
    public static final String AIR_RANK = "content:air:rank";
    public static final String AIR_DAILY = "content:air:daily";
    public static final String AIR_HOURLY = "content:air:hourly";
    // 生活指数相关
    public static final String LIFE_INDEX = "content:life:index";
    // 日出日落相关
    public static final String SUN_INFO = "content:sun:info";
    public static final String MOON_INFO = "content:moon:info";
    // 经纬度转换相关
    public static final String AMAP_ADDRESS = "content:amap:address";
    // 航班机场相关
    public static final String FLIGHT_INFO = "content:flight:info";
    public static final String AIRPORT_INFO = "content:airport:info";
    public static final String FLIGHT_NUMBER = "content:flight:number";
    public static final String FLIGHT_PRICE = "content:flight:price";
    public static final String FLIGHT_PRICE_2 = "content:flight:price:2";
    public static final String FLIGHT_PRICE_CACHE = "content:flight:price:cache";
    public static final String FLIGHT_PRICE_WAIT = "content:flight:price:wait";
    public static final String FLIGHT_PRICE_DEAD_QUEUE = "content:flight:price:dead_queue";
    public static final String FLIGHT_DAY_NUM = "content:flight:day:num";
    public static final String FLIGHT_DAY_NUM_2 = "content:flight:day:num:2";
    public static final String FLIGHT_TC_ACCESS_TOKEN = "content:flight:tc:access_token";
    // 菜谱相关
    public static final String COOKBOOK_CATALOG_KEY = "content:cookbook:catalog";
    public static final String COOKBOOK_KEYWORD_KEY = "content:cookbook:keyword";
    public static final String COOKBOOK_ID_KEY = "content:cookbook:id";
    // 城市活动
    public static final String CITY_EVENT_LIST = "content:cityEvent:list";
    public static final String CITY_EVENT_FILTER = "content:cityEvent:filter";
    public static final String CITY_EVENT_FILTER_EXACT = "content:cityEvent:filter:exact";
    // 万年历
    public static final String CALENDAR_HOLIDAY = "content:calendar:holiday";
    // 限行
    public static final String RESTRICTION_CITY = "content:restriction:city";
    public static final String RESTRICTION_CITY_DETAIL = "content:restriction:detail";
    // 电影
    public static final String MOVIE_HOT = "content:movie:hot";
    public static final String MOVIE_COMING = "content:movie:coming";
    public static final String MOVIE_DETAIL = "content:movie:detail";
    public static final String CINEMA_SHOWS = "content:cinema:shows";
    public static final String MOVIE_CINEMAS = "content:movie:cinemas";
    public static final String MOVIE_CINEMAS_BASE = "content:movie:cinemas:base";
    // 诗词
    public static final String POETRY = "content:poetry:list";
    // 火车票
    public static final String TRAIN_INFO = "content:train:info";
    public static final String TRAIN_INFO2 = "content:train:info2";
    public static final String TRAIN_SYNC_NUM = "content:train:day:num";
    public static final String TRAIN_SYNC_NUM_2 = "content:train:day:num2";
    // 赛事
    public static final String SPORTS_LEAGUE = "content:sports:league";
    public static final String SPORTS_MATCH = "content:sports:match";
    public static final String SPORTS_TEAM_RANK = "content:sports:teamRank";
    public static final String SPORTS_PLAYER_RANK = "content:sports:playerRank";

    public static long getTodayExpireTime() {
        // 获取当前时间
        long currentTimeMillis = System.currentTimeMillis();
        // 获取今天凌晨时间
        long todayTimeMillis =
                currentTimeMillis - (currentTimeMillis + TimeZone.getDefault().getRawOffset()) % (1000 * 3600 * 24);
        // 获取今天还剩多少秒
        return (todayTimeMillis + 24 * 60 * 60 * 1000 - currentTimeMillis) / 1000;
    }

    public static long getEndOfMonthExpireTime() {
        // 获取当前时间
        long currentTimeMillis = System.currentTimeMillis();
        LocalDate now = Instant.ofEpochMilli(currentTimeMillis).atZone(ZoneId.of("Asia/Shanghai")).toLocalDate();

        // 获取本月最后一天
        LocalDate lastDayOfMonth = now.with(TemporalAdjusters.lastDayOfMonth());

        // 获取本月最后一天的最后一秒的时间戳
        ZonedDateTime endOfMonthZonedDateTime = lastDayOfMonth.atTime(23, 59, 59).atZone(ZoneId.systemDefault());
        long endOfMonthTimeMillis = endOfMonthZonedDateTime.toInstant().toEpochMilli();

        // 计算剩余秒数
        return (endOfMonthTimeMillis - currentTimeMillis) / 1000;
    }

    /**
     * 生成带日期的风速数据 key
     *
     * @param prefix key 前缀
     * @param area   地区
     * @return 完整的 key
     */
    public static String getWindKeyWithDate(String prefix, String area) {
        return prefix + area + ":" + LocalDate.now().toString();
    }

    /**
     * 生成风速数据缓存键（无日期后缀）
     * 用于存储带元数据的风速数据
     *
     * @param prefix key 前缀
     * @param area   地区
     * @return 完整的 key
     */
    public static String getWindKey(String prefix, String area) {
        return prefix + area;
    }

    /**
     * 获取6天后的过期时间（秒）
     * 用于风速数据的长期缓存
     *
     * @return 6天的秒数
     */
    public static long getSixDaysExpireTime() {
        return 6L * 24L * 60L * 60L; // 518400秒
    }

    /**
     * 获取6天后的过期时间（秒）
     * 用于风速数据的长期缓存
     *
     * @return 6天的秒数
     */
    public static long getOneDaysExpireTime() {
        return 1L * 24L * 60L * 60L; // 518400秒
    }

    /**
     * 生成风速URL映射缓存键
     * 用于缓存 locationPath -> URL 的映射关系
     *
     * @param locationPath 地区路径
     * @return 完整的缓存键
     */
    public static String getWindUrlMappingKey(String locationPath) {
        return WEATHER_WIND_URL_MAPPING_PREFIX + locationPath;
    }

    /**
     * 生成风速URL数据缓存键
     * 用于缓存 URL -> WindData 的映射关系
     *
     * @param url 气象局URL
     * @return 完整的缓存键
     */
    public static String getWindUrlDataKey(String url) {
        // 使用URL的hash值作为key的一部分，避免特殊字符问题
        return WEATHER_WIND_URL_DATA_PREFIX + Math.abs(url.hashCode());
    }
}
