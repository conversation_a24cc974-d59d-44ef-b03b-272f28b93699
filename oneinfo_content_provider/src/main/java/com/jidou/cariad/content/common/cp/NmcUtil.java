package com.jidou.cariad.content.common.cp;

import com.alibaba.fastjson.JSON;
import com.jidou.cariad.content.base.weather.dao.WeatherNmcCityDO;
import com.jidou.cariad.content.base.weather.dao.entity.WeatherNmcProvinceDO;
import com.jidou.cariad.content.common.annotation.MyCache;
import com.jidou.cariad.content.common.content.RedisContent;
import com.jidou.cariad.content.common.enums.BusinessCode;
import com.jidou.cariad.content.common.enums.ElasticSearchIndex;
import com.jidou.cariad.content.common.es.ElasticsearchUtil;
import com.jidou.cariad.content.common.exception.exceptions.BizException;
import com.jidou.cariad.content.common.utils.HttpClientUtil;
import groovy.util.logging.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.opensearch.index.query.BoolQueryBuilder;
import org.opensearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;

/**
 * 气象局请求工具类
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class NmcUtil {

    @Value("${nmc.baseUrl:http://www.nmc.cn}")
    private String baseUrl;
    @Resource
    private ElasticsearchUtil elasticsearchUtil;


    public List<WeatherNmcProvinceDO> getProvinceList() {
        String url = baseUrl + "/rest/province/all";
        HashMap<String, String> param = new HashMap<>();
        param.put("_", String.valueOf(System.currentTimeMillis()));
        try {
            String s = HttpClientUtil.doGet(url, param, null);

            List<WeatherNmcProvinceDO> weatherNmcProvinceDOS = JSON.parseArray(s, WeatherNmcProvinceDO.class);
            return weatherNmcProvinceDOS;
        } catch (IOException e) {
            throw new BizException(BusinessCode.CP_REQUEST_ERROR);
        }
    }

    public List<WeatherNmcCityDO> getCityList(String provinceCode) {
        String url = baseUrl + "/rest/province/" + provinceCode;
        HashMap<String, String> param = new HashMap<>();
        param.put("_", String.valueOf(System.currentTimeMillis()));
        try {
            String s = HttpClientUtil.doGet(url, param, null);

            List<WeatherNmcCityDO> weatherNmcProvinceDOS = JSON.parseArray(s, WeatherNmcCityDO.class);
            return weatherNmcProvinceDOS;
        } catch (IOException e) {
            throw new BizException(BusinessCode.CP_REQUEST_ERROR);
        }
    }

    /**
     * 构建气象局网站URL
     * 使用缓存优化，避免重复的ElasticSearch查询
     *
     * @param locationPath 地区ID
     * @return 完整URL
     */
    @MyCache(key = RedisContent.WEATHER_WIND_URL_MAPPING_PREFIX, expire = 86400L)
    public String buildWeatherUrl(String locationPath, Boolean ignoreCache) {
        // 1. 首先尝试识别行政区划格式并分解
        String[] parts = locationPath.split(",");
        String district = parts.length > 0 ? parts[0] : "";  // 第一部分通常是区县
        String city = parts.length > 1 ? parts[1] : "";      // 第二部分通常是城市

        // 2. 构建更精确的查询
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        // 3. 添加最精确的匹配条件 - 同时匹配省市
        if (!district.isEmpty() && !city.isEmpty()) {
            BoolQueryBuilder exactMatchQuery = QueryBuilders.boolQuery()
                    .must(QueryBuilders.matchPhraseQuery("province", city).boost(3.0f))
                    .must(QueryBuilders.matchPhraseQuery("city", district).boost(5.0f));
            boolQueryBuilder.should(exactMatchQuery.boost(10.0f));  // 给予最高权重
        }

        // 4. 保留原有匹配方式作为兜底
        boolQueryBuilder.should(
                QueryBuilders.matchQuery("province", locationPath).analyzer("chinese_analyzer").boost(1.5f));
        boolQueryBuilder.should(
                QueryBuilders.matchQuery("city", locationPath).analyzer("chinese_analyzer").boost(2.0f));

        // 设置最小匹配条件
        boolQueryBuilder.minimumShouldMatch(1);

        // 执行搜索
        List<WeatherNmcCityDO> cityList = elasticsearchUtil.search(
                ElasticSearchIndex.WEATHER_NMC_CITY,
                boolQueryBuilder,
                WeatherNmcCityDO.class);

        // 如果找到匹配结果，使用第一个（最匹配）的城市代码或URL
        if (cityList != null && !cityList.isEmpty()) {
            WeatherNmcCityDO nmcCityDO = cityList.get(0);
            if (StringUtils.isNotBlank(nmcCityDO.getUrl())) {
                return baseUrl + nmcCityDO.getUrl();
            }
            throw new RuntimeException("未找到匹配的城市");
        }
        throw new RuntimeException("未找到匹配的城市");
    }
}
